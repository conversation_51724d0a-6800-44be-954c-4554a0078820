# Custom Button Block for Product Pages

## Overview
A fully customizable button block that can be added to product pages with configurable styling, text, and guarantee message.

## Features
- ✅ Customizable button text
- ✅ Configurable URL with option to open in new tab
- ✅ Full color customization (background, text, border, hover states)
- ✅ Optional guarantee text with custom styling
- ✅ Mobile responsive design
- ✅ Shopify theme standards compliant
- ✅ Hover animations
- ✅ Box shadow effects

## How to Use

### Adding the Button Block
1. Go to your Shopify admin
2. Navigate to Online Store > Themes
3. Click "Customize" on your active theme
4. Go to a product page
5. In the product template section, click "Add block"
6. Select "Custom Button" from the list
7. Configure the settings as needed

### Configuration Options

#### Button Settings
- **Button Text**: The text displayed on the button (default: "TAKE THE QUIZ")
- **Button URL**: The destination URL when the button is clicked
- **Open in new tab**: Check to open the link in a new browser tab

#### Guarantee Text Settings
- **Guarantee Text**: Optional text displayed below the button (default: "180-day money-back guarantee")

#### Button Style
- **Button Background Color**: Background color of the button (default: #009ee0)
- **Button Text Color**: Text color of the button (default: #ffffff)
- **Button Border Color**: Border color of the button (default: #009ee0)
- **Button Hover Background Color**: Background color on hover (default: #ffffff)
- **Button Hover Text Color**: Text color on hover (default: #009ee0)
- **Button Hover Border Color**: Border color on hover (default: #009ee0)

#### Guarantee Text Style
- **Guarantee Text Color**: Color of the guarantee text (default: #686868)

## Default Styling
The button follows the design specifications provided:
- Font: Lato, sans-serif
- Font size: 16px (responsive)
- Font weight: 600
- Height: 50px
- Border radius: 4px
- Box shadow: rgba(0, 158, 224, 0.4) 0px 4px 16px 0px
- Letter spacing: 0.571429px
- Transition: all 0.2s linear

## Mobile Responsiveness
The button automatically adapts to different screen sizes:
- **Tablet (≤768px)**: Smaller font size and padding
- **Mobile (≤480px)**: Further reduced size and spacing

## Technical Details
- Block type: `custom_button`
- Block limit: 1 per product page
- Compatible with all Shopify themes
- No external dependencies
- Lightweight implementation

## Browser Support
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers

## Customization
The button can be further customized by modifying the CSS in the main-product.liquid file. Look for the "Custom Button Styles" section.
